<template>
  <div class="house-form">
    <div class="simple-title">
      <span class="title-text">{{ isUpdate ? '编辑房屋信息' : '新增房屋信息' }}</span>
      <div class="title-tips">
        <Icon icon="ant-design:info-circle-outlined" class="tip-icon" />
        <span>请按照表单分组填写房屋资产信息，带 * 的字段为必填项</span>
      </div>
    </div>
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            基本信息
            <span class="section-tip">（包含资产基础信息和位置信息）</span>
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerBasicForm" :schemas="basicInfoSchema" />
        </div>
      </div>

      <!-- 房屋信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:home-outlined" class="title-icon" />
            房屋信息
            <span class="section-tip">（包含房屋用途、面积、价值等详细信息）</span>
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerHouseForm" :schemas="houseInfoSchema" />
        </div>
      </div>

      <!-- 资产使用情况 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:pie-chart-outlined" class="title-icon" />
            资产使用情况
            <span class="section-tip">（包含使用状态和各类型面积分布）</span>
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerAssetsForm" :schemas="assetsInfoSchema" />
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button type="primary" @click="handleSubmit" :loading="loading" size="large"> 提交 </a-button>
        <a-button @click="handleReset" style="margin-left: 12px" size="large"> 重置 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="house-form-component" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { basicInfoSchema, houseInfoSchema, assetsInfoSchema } from './houseForm.data';
  import { saveOrUpdate, getDetail } from './house.api';

  const route = useRoute();
  const router = useRouter();
  const { createMessage } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  // 表单数据
  const _formData = ref<any>({});

  // 注册表单 - 加大label宽度
  const [registerBasicForm, { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasic }] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: basicInfoSchema,
  });

  const [registerHouseForm, { resetFields: resetHouseFields, setFieldsValue: setHouseFieldsValue, validate: validateHouse }] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: houseInfoSchema,
  });

  const [registerAssetsForm, { resetFields: resetAssetsFields, setFieldsValue: setAssetsFieldsValue, validate: validateAssets }] = useForm({
    labelWidth: 180,
    baseColProps: { span: 8 },
    showActionButtonGroup: false,
    schemas: assetsInfoSchema,
  });

  // 获取标题
  const _getTitle = computed(() => (!isUpdate.value ? '新增房屋信息' : '编辑房屋信息'));

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const path = route.path;
    if (path.includes('/edit/')) {
      isUpdate.value = true;
      recordId.value = route.params.id as string;
      await loadData();
    } else {
      isUpdate.value = false;
      resetFields();
      // 设置默认值
      setDefaultValues();
    }
  });

  // 设置默认值
  function setDefaultValues() {
    setBasicFieldsValue({
      groupName: 0, // 默认所属集团
      status: 0, // 默认草稿状态
      reportOrNot: 0, // 默认不报送国资委
      entryClerk: '当前用户', // 录入人
      createTime: new Date().toISOString().split('T')[0], // 录入时间
    });
    setHouseFieldsValue({
      property: 0, // 默认无产权证
      offAccount: 0, // 默认非账外
      insuranceOrNot: 0, // 默认不投保
      mortgageOrNot: 0, // 默认不抵押
      dangerousHouseOrNot: 0, // 默认非危房
      completionOrNot: 0, // 默认未竣工
      owingOrNot: 0, // 默认不拖欠
      vitalizeOrNot: 0, // 默认无盘活价值
    });
    setAssetsFieldsValue({
      assetsStatus: [], // 默认空数组
    });
  }

  // 加载数据
  async function loadData() {
    try {
      const record = await getDetail(recordId.value);
      setFieldsValue(record);
    } catch (error) {
      createMessage.error('加载数据失败');
    }
  }

  // 设置表单值
  function setFieldsValue(data: any) {
    // 基本信息
    setBasicFieldsValue({
      code: data.code,
      enterpriseCode: data.enterpriseCode,
      name: data.name,
      groupName: data.groupName,
      companyName: data.companyName,
      ownUnit: data.ownUnit,
      manageUnit: data.manageUnit,
      region: [data.province, data.city, data.area],
      address: data.address,
      status: data.status,
      reportOrNot: data.reportOrNot,
      operator: data.operator,
      entryClerk: data.entryClerk,
      createTime: data.createTime,
    });

    // 房屋信息
    setHouseFieldsValue({
      houseTypes: data.houseTypes,
      useTypeInput: data.useTypeInput,
      gainDate: data.gainDate,
      assetEntryDate: data.assetEntryDate,
      source: data.source,
      totalArea: data.totalArea,
      rentableArea: data.rentableArea,
      propertyArea: data.propertyArea,
      notPropertyArea: data.notPropertyArea,
      assetsAmount: data.assetsAmount,
      bookAmount: data.bookAmount,
      dateOfBookValue: data.dateOfBookValue,
      property: data.property,
      warrantDate: data.warrantDate,
      custodyEntrustingParty: data.custodyEntrustingParty,
      offAccount: data.offAccount,
      insuranceOrNot: data.insuranceOrNot,
      mortgageOrNot: data.mortgageOrNot,
      dangerousHouseOrNot: data.dangerousHouseOrNot,
      completionOrNot: data.completionOrNot,
      owingOrNot: data.owingOrNot,
      vitalizeOrNot: data.vitalizeOrNot,
      workProgress: data.workProgress,
      problems: data.problems,
    });

    // 资产使用情况
    setAssetsFieldsValue({
      assetsStatus: data.assetsStatus,
      idleArea: data.idleArea,
      useArea: data.useArea,
      rentArea: data.rentArea,
      lendArea: data.lendArea,
      occupyArea: data.occupyArea,
      sellArea: data.sellArea,
      otherArea: data.otherArea,
      remark: data.remark,
    });
  }

  // 重置表单
  function resetFields() {
    resetBasicFields();
    resetHouseFields();
    resetAssetsFields();
  }

  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true;
      
      // 验证所有表单
      const [basicData, houseData, assetsData] = await Promise.all([validateBasic(), validateHouse(), validateAssets()]);

      // 合并数据
      const formData = {
        ...basicData,
        ...houseData,
        ...assetsData,
        // 处理地区数据
        province: basicData.region?.[0] || '',
        city: basicData.region?.[1] || '',
        area: basicData.region?.[2] || '',
      };

      // 如果是编辑模式，添加ID
      if (isUpdate.value) {
        formData.id = recordId.value;
      }

      await saveOrUpdate(formData, isUpdate.value);
      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      router.push('/assetsInfo/house');
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 重置
  function handleReset() {
    resetFields();
    if (!isUpdate.value) {
      setDefaultValues();
    }
  }
</script>

<style lang="less" scoped>
  .house-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 20px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .title-text {
        display: block;
        margin-bottom: 8px;
        font-size: 22px;
        color: #1890ff;
      }

      .title-tips {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        font-weight: normal;

        .tip-icon {
          margin-right: 6px;
          color: #1890ff;
        }
      }
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }

          .section-tip {
            margin-left: 8px;
            font-size: 13px;
            color: #666;
            font-weight: normal;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }
  }
</style>
